package com.ideal.envc.service.impl;

import com.ideal.envc.common.FileOperationUtils;
import com.ideal.envc.config.ContrastResultConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * FileOperationServiceImpl单元测试
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("FileOperationServiceImpl单元测试")
class FileOperationServiceImplTest {

    @Mock
    private ContrastResultConfig contrastResultConfig;

    @InjectMocks
    private FileOperationServiceImpl fileOperationService;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }

    @Test
    @DisplayName("测试生成结果文件路径")
    void testGenerateResultFilePath() {
        // 准备测试数据
        String flowId = "12345";
        String basePath = "/test/path";
        
        doReturn(basePath).when(contrastResultConfig).getFileStorageBasePath();

        // 执行测试方法
        String result = fileOperationService.generateResultFilePath(flowId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains(flowId));
        assertTrue(result.contains(basePath));
        assertTrue(result.endsWith(".json"));
    }

    @Test
    @DisplayName("测试生成结果文件路径 - 基础路径为空")
    void testGenerateResultFilePath_EmptyBasePath() {
        // 准备测试数据
        String flowId = "12345";
        
        doReturn("").when(contrastResultConfig).getFileStorageBasePath();

        // 执行测试方法
        String result = fileOperationService.generateResultFilePath(flowId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains(flowId));
        assertTrue(result.endsWith(".json"));
    }

    @Test
    @DisplayName("测试生成结果文件路径 - 基础路径为null")
    void testGenerateResultFilePath_NullBasePath() {
        // 准备测试数据
        String flowId = "12345";
        
        doReturn(null).when(contrastResultConfig).getFileStorageBasePath();

        // 执行测试方法
        String result = fileOperationService.generateResultFilePath(flowId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains(flowId));
        assertTrue(result.endsWith(".json"));
    }

    @Test
    @DisplayName("测试保存结果文件 - 文件存储启用")
    void testSaveResultFile_FileStorageEnabled() {
        // 准备测试数据
        String content = "test content";
        String flowId = "12345";
        String basePath = "/test/path";
        
        doReturn(true).when(contrastResultConfig).isFileStorageEnabled();
        doReturn(basePath).when(contrastResultConfig).getFileStorageBasePath();

        try (MockedStatic<FileOperationUtils> mockedUtils = mockStatic(FileOperationUtils.class)) {
            mockedUtils.when(() -> FileOperationUtils.createFile(anyString(), anyString())).thenReturn(true);

            // 执行测试方法
            boolean result = fileOperationService.saveResultFile(content, flowId);

            // 验证结果
            assertTrue(result);
            mockedUtils.verify(() -> FileOperationUtils.createFile(eq(content), anyString()));
        }
    }

    @Test
    @DisplayName("测试保存结果文件 - 文件存储未启用")
    void testSaveResultFile_FileStorageDisabled() {
        // 准备测试数据
        String content = "test content";
        String flowId = "12345";
        
        doReturn(false).when(contrastResultConfig).isFileStorageEnabled();

        // 执行测试方法
        boolean result = fileOperationService.saveResultFile(content, flowId);

        // 验证结果
        assertFalse(result);
    }

    @Test
    @DisplayName("测试保存结果文件 - 保存失败")
    void testSaveResultFile_SaveFailed() {
        // 准备测试数据
        String content = "test content";
        String flowId = "12345";
        String basePath = "/test/path";
        
        doReturn(true).when(contrastResultConfig).isFileStorageEnabled();
        doReturn(basePath).when(contrastResultConfig).getFileStorageBasePath();

        try (MockedStatic<FileOperationUtils> mockedUtils = mockStatic(FileOperationUtils.class)) {
            mockedUtils.when(() -> FileOperationUtils.createFile(anyString(), anyString())).thenReturn(false);

            // 执行测试方法
            boolean result = fileOperationService.saveResultFile(content, flowId);

            // 验证结果
            assertFalse(result);
        }
    }

    @Test
    @DisplayName("测试保存结果文件 - 异常处理")
    void testSaveResultFile_Exception() {
        // 准备测试数据
        String content = "test content";
        String flowId = "12345";
        String basePath = "/test/path";
        
        doReturn(true).when(contrastResultConfig).isFileStorageEnabled();
        doReturn(basePath).when(contrastResultConfig).getFileStorageBasePath();

        try (MockedStatic<FileOperationUtils> mockedUtils = mockStatic(FileOperationUtils.class)) {
            mockedUtils.when(() -> FileOperationUtils.createFile(anyString(), anyString()))
                     .thenThrow(new RuntimeException("文件操作异常"));

            // 执行测试方法
            boolean result = fileOperationService.saveResultFile(content, flowId);

            // 验证结果
            assertFalse(result);
        }
    }

    @Test
    @DisplayName("测试保存结果文件到指定路径")
    void testSaveResultFileOfPath() {
        // 准备测试数据
        String content = "test content";
        String filePath = "/test/path/file.json";

        try (MockedStatic<FileOperationUtils> mockedUtils = mockStatic(FileOperationUtils.class)) {
            mockedUtils.when(() -> FileOperationUtils.createFile(content, filePath)).thenReturn(true);

            // 执行测试方法
            boolean result = fileOperationService.saveResultFileOfPath(content, filePath);

            // 验证结果
            assertTrue(result);
            mockedUtils.verify(() -> FileOperationUtils.createFile(content, filePath));
        }
    }

    @Test
    @DisplayName("测试保存结果文件到指定路径 - 保存失败")
    void testSaveResultFileOfPath_Failed() {
        // 准备测试数据
        String content = "test content";
        String filePath = "/test/path/file.json";

        try (MockedStatic<FileOperationUtils> mockedUtils = mockStatic(FileOperationUtils.class)) {
            mockedUtils.when(() -> FileOperationUtils.createFile(content, filePath)).thenReturn(false);

            // 执行测试方法
            boolean result = fileOperationService.saveResultFileOfPath(content, filePath);

            // 验证结果
            assertFalse(result);
        }
    }

    @Test
    @DisplayName("测试读取结果文件")
    void testReadResultFile() {
        // 准备测试数据
        String filePath = "/test/path/file.json";
        String expectedContent = "test content";

        try (MockedStatic<FileOperationUtils> mockedUtils = mockStatic(FileOperationUtils.class)) {
            mockedUtils.when(() -> FileOperationUtils.readFile(filePath)).thenReturn(expectedContent);

            // 执行测试方法
            String result = fileOperationService.readResultFile(filePath);

            // 验证结果
            assertEquals(expectedContent, result);
            mockedUtils.verify(() -> FileOperationUtils.readFile(filePath));
        }
    }

    @Test
    @DisplayName("测试读取结果文件 - 读取失败")
    void testReadResultFile_Failed() {
        // 准备测试数据
        String filePath = "/test/path/file.json";

        try (MockedStatic<FileOperationUtils> mockedUtils = mockStatic(FileOperationUtils.class)) {
            mockedUtils.when(() -> FileOperationUtils.readFile(filePath)).thenReturn(null);

            // 执行测试方法
            String result = fileOperationService.readResultFile(filePath);

            // 验证结果
            assertNull(result);
        }
    }

    @Test
    @DisplayName("测试读取结果文件 - 异常处理")
    void testReadResultFile_Exception() {
        // 准备测试数据
        String filePath = "/test/path/file.json";

        try (MockedStatic<FileOperationUtils> mockedUtils = mockStatic(FileOperationUtils.class)) {
            mockedUtils.when(() -> FileOperationUtils.readFile(filePath))
                     .thenThrow(new RuntimeException("文件读取异常"));

            // 执行测试方法
            String result = fileOperationService.readResultFile(filePath);

            // 验证结果
            assertNull(result);
        }
    }

    @Test
    @DisplayName("测试构造函数")
    void testConstructor() {
        // 创建新的服务实例
        FileOperationServiceImpl service = new FileOperationServiceImpl(contrastResultConfig);

        // 验证实例创建成功
        assertNotNull(service);
    }

    @Test
    @DisplayName("测试文件存储功能检查 - 启用")
    void testIsFileStorageEnabled_Enabled() {
        doReturn(true).when(contrastResultConfig).isFileStorageEnabled();
        
        String content = "test content";
        String flowId = "12345";
        String basePath = "/test/path";
        doReturn(basePath).when(contrastResultConfig).getFileStorageBasePath();

        try (MockedStatic<FileOperationUtils> mockedUtils = mockStatic(FileOperationUtils.class)) {
            mockedUtils.when(() -> FileOperationUtils.createFile(anyString(), anyString())).thenReturn(true);

            boolean result = fileOperationService.saveResultFile(content, flowId);
            assertTrue(result);
        }
    }

    @Test
    @DisplayName("测试文件存储功能检查 - 未启用")
    void testIsFileStorageEnabled_Disabled() {
        doReturn(false).when(contrastResultConfig).isFileStorageEnabled();
        
        String content = "test content";
        String flowId = "12345";

        boolean result = fileOperationService.saveResultFile(content, flowId);
        assertFalse(result);
    }

    @Test
    @DisplayName("测试文件存储功能检查 - 配置为null")
    void testIsFileStorageEnabled_ConfigNull() {
        // 创建没有配置的服务实例
        FileOperationServiceImpl serviceWithoutConfig = new FileOperationServiceImpl(null);
        
        String content = "test content";
        String flowId = "12345";

        boolean result = serviceWithoutConfig.saveResultFile(content, flowId);
        assertFalse(result);
    }
}
