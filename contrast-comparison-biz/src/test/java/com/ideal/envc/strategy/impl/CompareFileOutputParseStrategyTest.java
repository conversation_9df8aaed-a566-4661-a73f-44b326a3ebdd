package com.ideal.envc.strategy.impl;

import com.alibaba.fastjson2.JSON;
import com.ideal.envc.compare.CompareSourceTargetContentManagerV2;
import com.ideal.envc.model.bean.EngineActOutputBean;
import com.ideal.envc.model.dto.ContentCustomDto;
import com.ideal.envc.model.dto.EngineCompareOutPutDto;
import com.ideal.envc.model.dto.OutputParseResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;

/**
 * CompareFileOutputParseStrategy单元测试
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class CompareFileOutputParseStrategyTest {

    @InjectMocks
    private CompareFileOutputParseStrategy strategy;

    private EngineActOutputBean engineActOutputBean;

    @BeforeEach
    void setUp() {
        engineActOutputBean = new EngineActOutputBean();
    }

    @Test
    @DisplayName("测试解析输出 - 空输出列表")
    void testParse_EmptyOutputList() {
        // 准备测试数据
        List<EngineActOutputBean> actOutputs = new ArrayList<>();
        String actDefName = "COMPARE_FILE";

        // 执行测试方法
        OutputParseResult result = strategy.parse(actOutputs, actDefName);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isRet());
        assertEquals("", result.getContent());
    }

    @Test
    @DisplayName("测试解析输出 - null输出列表")
    void testParse_NullOutputList() {
        // 准备测试数据
        List<EngineActOutputBean> actOutputs = null;
        String actDefName = "COMPARE_FILE";

        // 执行测试方法
        OutputParseResult result = strategy.parse(actOutputs, actDefName);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isRet());
        assertEquals("", result.getContent());
    }

    @Test
    @DisplayName("测试解析输出 - 比对成功场景")
    void testParse_Success() {
        // 准备测试数据
        EngineCompareOutPutDto engineCompareOutPutDto = new EngineCompareOutPutDto();
        engineCompareOutPutDto.setRet(true);
        engineCompareOutPutDto.setCompareResult("文件比对一致");
        engineCompareOutPutDto.setSourceContent("源文件内容");
        engineCompareOutPutDto.setTargetContent("目标文件内容");

        String outputJson = JSON.toJSONString(engineCompareOutPutDto);
        engineActOutputBean.setOutput(outputJson);

        List<EngineActOutputBean> actOutputs = Arrays.asList(engineActOutputBean);
        String actDefName = "COMPARE_FILE";

        // Mock CompareSourceTargetContentManagerV2
        Map<String, Object> compareResult = new HashMap<>();
        compareResult.put(CompareSourceTargetContentManagerV2.COMPARERESULT, "比对结果内容");
        compareResult.put(CompareSourceTargetContentManagerV2.RET, true);

        try (MockedStatic<CompareSourceTargetContentManagerV2> mockedManager = mockStatic(CompareSourceTargetContentManagerV2.class)) {
            CompareSourceTargetContentManagerV2 mockInstance = org.mockito.Mockito.mock(CompareSourceTargetContentManagerV2.class);
            mockedManager.when(CompareSourceTargetContentManagerV2::getInstance).thenReturn(mockInstance);
            org.mockito.Mockito.when(mockInstance.compare(anyString(), anyString())).thenReturn(compareResult);

            // 执行测试方法
            OutputParseResult result = strategy.parse(actOutputs, actDefName);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isRet());
            assertNotNull(result.getContent());

            // 解析返回的内容
            ContentCustomDto contentCustomDto = JSON.parseObject(result.getContent(), ContentCustomDto.class);
            assertNotNull(contentCustomDto);
            assertTrue(contentCustomDto.isRet());
            assertEquals("比对结果内容", contentCustomDto.getContent());
            assertEquals("源文件内容", contentCustomDto.getSourceContent());
            assertEquals("目标文件内容", contentCustomDto.getTargetContent());
        }
    }

    @Test
    @DisplayName("测试解析输出 - 比对失败场景")
    void testParse_Failure() {
        // 准备测试数据
        EngineCompareOutPutDto engineCompareOutPutDto = new EngineCompareOutPutDto();
        engineCompareOutPutDto.setRet(false);
        engineCompareOutPutDto.setCompareResult("文件比对不一致");
        engineCompareOutPutDto.setSourceContent("源文件内容");
        engineCompareOutPutDto.setTargetContent("目标文件内容不一致");
        
        String outputJson = JSON.toJSONString(engineCompareOutPutDto);
        engineActOutputBean.setOutput(outputJson);
        
        List<EngineActOutputBean> actOutputs = Arrays.asList(engineActOutputBean);
        String actDefName = "COMPARE_FILE";

        // Mock CompareSourceTargetContentManagerV2
        Map<String, Object> compareResult = new HashMap<>();
        compareResult.put(CompareSourceTargetContentManagerV2.COMPARERESULT, "文件比对结果");
        compareResult.put(CompareSourceTargetContentManagerV2.RET, false);

        try (MockedStatic<CompareSourceTargetContentManagerV2> mockedManager = mockStatic(CompareSourceTargetContentManagerV2.class)) {
            CompareSourceTargetContentManagerV2 mockInstance = org.mockito.Mockito.mock(CompareSourceTargetContentManagerV2.class);
            mockedManager.when(CompareSourceTargetContentManagerV2::getInstance).thenReturn(mockInstance);
            org.mockito.Mockito.when(mockInstance.compare(anyString(), anyString())).thenReturn(compareResult);

            // 执行测试方法
            OutputParseResult result = strategy.parse(actOutputs, actDefName);

            // 验证结果
            assertNotNull(result);
            assertFalse(result.isRet());
            assertNotNull(result.getContent());

            // 解析返回的内容
            ContentCustomDto contentCustomDto = JSON.parseObject(result.getContent(), ContentCustomDto.class);
            assertNotNull(contentCustomDto);
            assertFalse(contentCustomDto.isRet());
            assertEquals("文件比对结果", contentCustomDto.getContent());
            assertEquals("源文件内容", contentCustomDto.getSourceContent());
            assertEquals("目标文件内容不一致", contentCustomDto.getTargetContent());
        }
    }

    @Test
    @DisplayName("测试解析输出 - ret为null场景")
    void testParse_NullRet() {
        // 准备测试数据
        EngineCompareOutPutDto engineCompareOutPutDto = new EngineCompareOutPutDto();
        engineCompareOutPutDto.setRet(null); // 设置ret为null
        engineCompareOutPutDto.setCompareResult("文件比对结果");
        
        String outputJson = JSON.toJSONString(engineCompareOutPutDto);
        engineActOutputBean.setOutput(outputJson);
        
        List<EngineActOutputBean> actOutputs = Arrays.asList(engineActOutputBean);
        String actDefName = "COMPARE_FILE";

        // 执行测试方法
        OutputParseResult result = strategy.parse(actOutputs, actDefName);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isRet()); // 当ret为null时，应该默认为false
        
        // 解析返回的内容
        ContentCustomDto contentCustomDto = JSON.parseObject(result.getContent(), ContentCustomDto.class);
        assertNotNull(contentCustomDto);
        assertFalse(contentCustomDto.isRet());
        assertEquals("文件比对结果", contentCustomDto.getContent());
    }

    @Test
    @DisplayName("测试解析输出 - 无效JSON格式")
    void testParse_InvalidJson() {
        // 准备测试数据 - 无效的JSON格式
        String outputJson = "这不是一个有效的JSON字符串";
        engineActOutputBean.setOutput(outputJson);
        
        List<EngineActOutputBean> actOutputs = Arrays.asList(engineActOutputBean);
        String actDefName = "COMPARE_FILE";

        // 执行测试方法
        OutputParseResult result = strategy.parse(actOutputs, actDefName);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isRet());
        assertEquals("", result.getContent());
    }

    @Test
    @DisplayName("测试解析输出 - 空输出内容")
    void testParse_EmptyOutput() {
        // 准备测试数据 - 空输出内容
        engineActOutputBean.setOutput("");
        
        List<EngineActOutputBean> actOutputs = Arrays.asList(engineActOutputBean);
        String actDefName = "COMPARE_FILE";

        // 执行测试方法
        OutputParseResult result = strategy.parse(actOutputs, actDefName);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isRet());
        assertEquals("", result.getContent());
    }

    @Test
    @DisplayName("测试获取策略类型")
    void testGetType() {
        // 执行测试方法
        String type = strategy.getType();
        
        // 验证结果
        assertEquals("COMPARE_FILE", type);
    }
}
