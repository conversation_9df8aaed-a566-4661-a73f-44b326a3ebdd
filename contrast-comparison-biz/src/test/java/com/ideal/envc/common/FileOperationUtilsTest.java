package com.ideal.envc.common;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;

/**
 * FileOperationUtils单元测试
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("FileOperationUtils单元测试")
class FileOperationUtilsTest {

    @TempDir
    Path tempDir;

    private String testFilePath;
    private String testContent;

    @BeforeEach
    void setUp() {
        testFilePath = tempDir.resolve("test.json").toString();
        testContent = "测试文件内容";
    }

    @AfterEach
    void tearDown() {
        // 清理测试文件
        try {
            Path path = Paths.get(testFilePath);
            if (Files.exists(path)) {
                Files.delete(path);
            }
        } catch (IOException e) {
            // 忽略清理异常
        }
    }

    @Test
    @DisplayName("测试创建文件 - 成功")
    void testCreateFile_Success() {
        // 执行测试方法
        boolean result = FileOperationUtils.createFile(testContent, testFilePath);

        // 验证结果
        assertTrue(result);
        assertTrue(Files.exists(Paths.get(testFilePath)));
        
        // 验证文件内容
        String readContent = FileOperationUtils.readFile(testFilePath);
        assertEquals(testContent, readContent);
    }

    @Test
    @DisplayName("测试创建文件 - 空内容")
    void testCreateFile_EmptyContent() {
        // 执行测试方法
        boolean result = FileOperationUtils.createFile("", testFilePath);

        // 验证结果
        assertTrue(result);
        assertTrue(Files.exists(Paths.get(testFilePath)));
        
        // 验证文件内容
        String readContent = FileOperationUtils.readFile(testFilePath);
        assertEquals("", readContent);
    }

    @Test
    @DisplayName("测试创建文件 - null内容")
    void testCreateFile_NullContent() {
        // 执行测试方法
        boolean result = FileOperationUtils.createFile(null, testFilePath);

        // 验证结果
        assertTrue(result);
        assertTrue(Files.exists(Paths.get(testFilePath)));
        
        // 验证文件内容
        String readContent = FileOperationUtils.readFile(testFilePath);
        assertEquals("", readContent);
    }

    @Test
    @DisplayName("测试创建文件 - 空文件路径")
    void testCreateFile_EmptyFilePath() {
        // 执行测试方法
        boolean result = FileOperationUtils.createFile(testContent, "");

        // 验证结果
        assertFalse(result);
    }

    @Test
    @DisplayName("测试创建文件 - null文件路径")
    void testCreateFile_NullFilePath() {
        // 执行测试方法
        boolean result = FileOperationUtils.createFile(testContent, null);

        // 验证结果
        assertFalse(result);
    }

    @Test
    @DisplayName("测试创建文件 - 创建父目录")
    void testCreateFile_CreateParentDirectory() {
        // 准备测试数据 - 包含不存在的父目录
        String nestedFilePath = tempDir.resolve("nested/dir/test.json").toString();

        // 执行测试方法
        boolean result = FileOperationUtils.createFile(testContent, nestedFilePath);

        // 验证结果
        assertTrue(result);
        assertTrue(Files.exists(Paths.get(nestedFilePath)));
        
        // 验证文件内容
        String readContent = FileOperationUtils.readFile(nestedFilePath);
        assertEquals(testContent, readContent);
    }

    @Test
    @DisplayName("测试读取文件 - 成功")
    void testReadFile_Success() throws IOException {
        // 准备测试数据 - 先创建文件
        Files.write(Paths.get(testFilePath), testContent.getBytes("UTF-8"));

        // 执行测试方法
        String result = FileOperationUtils.readFile(testFilePath);

        // 验证结果
        assertEquals(testContent, result);
    }

    @Test
    @DisplayName("测试读取文件 - 文件不存在")
    void testReadFile_FileNotExists() {
        // 准备测试数据 - 不存在的文件路径
        String nonExistentPath = tempDir.resolve("nonexistent.json").toString();

        // 执行测试方法
        String result = FileOperationUtils.readFile(nonExistentPath);

        // 验证结果
        assertNull(result);
    }

    @Test
    @DisplayName("测试读取文件 - 空文件路径")
    void testReadFile_EmptyFilePath() {
        // 执行测试方法
        String result = FileOperationUtils.readFile("");

        // 验证结果
        assertNull(result);
    }

    @Test
    @DisplayName("测试读取文件 - null文件路径")
    void testReadFile_NullFilePath() {
        // 执行测试方法
        String result = FileOperationUtils.readFile(null);

        // 验证结果
        assertNull(result);
    }

    @Test
    @DisplayName("测试读取文件 - 空文件")
    void testReadFile_EmptyFile() throws IOException {
        // 准备测试数据 - 创建空文件
        Files.write(Paths.get(testFilePath), "".getBytes("UTF-8"));

        // 执行测试方法
        String result = FileOperationUtils.readFile(testFilePath);

        // 验证结果
        assertEquals("", result);
    }

    @Test
    @DisplayName("测试更新文件 - 替换模式")
    void testUpdateFile_ReplaceMode() throws IOException {
        // 准备测试数据 - 先创建文件
        String originalContent = "原始内容";
        Files.write(Paths.get(testFilePath), originalContent.getBytes("UTF-8"));

        String newContent = "新内容";

        // 执行测试方法
        boolean result = FileOperationUtils.updateFile(newContent, testFilePath, true);

        // 验证结果
        assertTrue(result);
        String readContent = FileOperationUtils.readFile(testFilePath);
        assertEquals(newContent, readContent);
    }

    @Test
    @DisplayName("测试更新文件 - 追加模式")
    void testUpdateFile_AppendMode() throws IOException {
        // 准备测试数据 - 先创建文件
        String originalContent = "原始内容";
        Files.write(Paths.get(testFilePath), originalContent.getBytes("UTF-8"));

        String appendContent = "追加内容";

        // 执行测试方法
        boolean result = FileOperationUtils.updateFile(appendContent, testFilePath, false);

        // 验证结果
        assertTrue(result);
        String readContent = FileOperationUtils.readFile(testFilePath);
        assertEquals(originalContent + appendContent, readContent);
    }

    @Test
    @DisplayName("测试更新文件 - 文件不存在时创建")
    void testUpdateFile_CreateWhenNotExists() {
        // 准备测试数据 - 不存在的文件
        String nonExistentPath = tempDir.resolve("new_file.json").toString();

        // 执行测试方法
        boolean result = FileOperationUtils.updateFile(testContent, nonExistentPath, true);

        // 验证结果
        assertTrue(result);
        assertTrue(Files.exists(Paths.get(nonExistentPath)));
        String readContent = FileOperationUtils.readFile(nonExistentPath);
        assertEquals(testContent, readContent);
    }

    @Test
    @DisplayName("测试替换文件 - 成功")
    void testReplaceFile_Success() throws IOException {
        // 准备测试数据 - 先创建文件
        String originalContent = "原始内容";
        Files.write(Paths.get(testFilePath), originalContent.getBytes("UTF-8"));

        String newContent = "替换内容";

        // 执行测试方法
        boolean result = FileOperationUtils.replaceFile(newContent, testFilePath);

        // 验证结果
        assertTrue(result);
        String readContent = FileOperationUtils.readFile(testFilePath);
        assertEquals(newContent, readContent);
    }

    @Test
    @DisplayName("测试追加文件 - 成功")
    void testAppendFile_Success() throws IOException {
        // 准备测试数据 - 先创建文件
        String originalContent = "原始内容";
        Files.write(Paths.get(testFilePath), originalContent.getBytes("UTF-8"));

        String appendContent = "追加内容";

        // 执行测试方法
        boolean result = FileOperationUtils.appendFile(appendContent, testFilePath);

        // 验证结果
        assertTrue(result);
        String readContent = FileOperationUtils.readFile(testFilePath);
        assertEquals(originalContent + appendContent, readContent);
    }

    @Test
    @DisplayName("测试生成时间戳文件名")
    void testGenerateTimestampFileName() {
        // 执行测试方法
        String fileName1 = FileOperationUtils.generateTimestampFileName("test", ".json");
        String fileName2 = FileOperationUtils.generateTimestampFileName("test", ".json");

        // 验证结果
        assertNotNull(fileName1);
        assertNotNull(fileName2);
        assertTrue(fileName1.startsWith("test"));
        assertTrue(fileName1.endsWith(".json"));
        assertTrue(fileName2.startsWith("test"));
        assertTrue(fileName2.endsWith(".json"));
        
        // 两次生成的文件名应该不同（因为时间戳不同）
        assertNotEquals(fileName1, fileName2);
    }

    @Test
    @DisplayName("测试UTF-8编码处理")
    void testUtf8Encoding() throws IOException {
        // 准备测试数据 - 包含中文字符
        String chineseContent = "测试中文内容：你好世界！";

        // 执行测试方法
        boolean createResult = FileOperationUtils.createFile(chineseContent, testFilePath);
        String readContent = FileOperationUtils.readFile(testFilePath);

        // 验证结果
        assertTrue(createResult);
        assertEquals(chineseContent, readContent);
    }
}
