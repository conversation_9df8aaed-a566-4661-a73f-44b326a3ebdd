package com.ideal.envc.component;

import com.ideal.envc.mapper.EngineActOutputMapper;
import com.ideal.envc.mapper.RunFlowMapper;
import com.ideal.envc.mapper.RunFlowResultMapper;
import com.ideal.envc.mapper.RunRuleMapper;
import com.ideal.envc.model.bean.EngineActOutputBean;
import com.ideal.envc.model.dto.OutputParseResult;
import com.ideal.envc.model.entity.RunFlowEntity;
import com.ideal.envc.model.entity.RunFlowResultEntity;
import com.ideal.envc.model.entity.RunRuleEntity;
import com.ideal.envc.model.enums.ActivityStateEnum;
import com.ideal.envc.model.enums.ActivityTypeEnum;
import com.ideal.envc.model.enums.FlowStateEnum;
import com.ideal.envc.model.enums.ScriptActivityNameEnum;
import com.ideal.envc.service.IRunInstanceInfoStateProcessService;
import com.ideal.envc.service.IFileOperationService;
import com.ideal.envc.config.ContrastResultConfig;
import com.ideal.envc.strategy.OutputParseStrategy;
import com.ideal.envc.strategy.OutputParseStrategyFactory;
import com.ideal.monitor.mq.MonitorFlowDto;
import com.ideal.monitor.mq.MonitorFowActiveNodeDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * EngineDataExecuteComponent单元测试
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("EngineDataExecuteComponent单元测试")
class EngineDataExecuteComponentTest {

    @Mock
    private RunFlowResultMapper runFlowResultEntityMapper;

    @Mock
    private RunRuleMapper runRuleMapper;

    @Mock
    private RunFlowMapper runFlowMapper;

    @Mock
    private IRunInstanceInfoStateProcessService runInstanceInfoStateProcessService;

    @Mock
    private EngineActOutputMapper engineActOutputMapper;

    @Mock
    private OutputParseStrategyFactory outputParseStrategyFactory;

    @Mock
    private OutputParseStrategy outputParseStrategy;

    @Mock
    private ContrastResultConfig contrastResultConfig;

    @Mock
    private IFileOperationService fileOperationService;

    @InjectMocks
    private EngineDataExecuteComponent component;

    private MonitorFlowDto monitorFlowDto;
    private MonitorFowActiveNodeDto activeNodeDto;
    private RunFlowEntity runFlowEntity;
    private RunRuleEntity runRuleEntity;
    private RunFlowResultEntity runFlowResultEntity;

    @BeforeEach
    void setUp() {
        monitorFlowDto = new MonitorFlowDto();
        monitorFlowDto.setFlowId(1L);
        monitorFlowDto.setFlowStatus(FlowStateEnum.FINISHED.getCode());
        monitorFlowDto.setBizUniqueId(100L);
        monitorFlowDto.setDateTime(new Timestamp(System.currentTimeMillis()));
        monitorFlowDto.setFlowStartTime(System.currentTimeMillis());
        monitorFlowDto.setFlowEndTime(System.currentTimeMillis() + 1000);
        monitorFlowDto.setUpdateOrderTime(System.currentTimeMillis());

        activeNodeDto = new MonitorFowActiveNodeDto();
        activeNodeDto.setReqId("test_req_id");
        activeNodeDto.setActStatus(ActivityStateEnum.ACT_STATE_FINISH.getCode());
        activeNodeDto.setActDefName(ActivityTypeEnum.COMPARE_CONTENT.getCode());
        activeNodeDto.setActName("test_activity");

        runFlowEntity = new RunFlowEntity();
        runFlowEntity.setId(1L);
        runFlowEntity.setRunBizId(200L);

        runRuleEntity = new RunRuleEntity();
        runRuleEntity.setId(200L);
        runRuleEntity.setEnvcRunInstanceInfoId(300L);
        runRuleEntity.setResult(-1);
        runRuleEntity.setModel(1);
        runRuleEntity.setType(1L);

        runFlowResultEntity = new RunFlowResultEntity();
        runFlowResultEntity.setId(1L);
    }

    @Test
    @DisplayName("测试处理流程信息 - 完成状态")
    void testExecuteMonitorData_FlowInfo_Completed() {
        // 准备测试数据
        monitorFlowDto.setMonitorFowActiveNodeDto(null);

        // Mock方法调用
        when(runFlowMapper.selectRunFlowById(monitorFlowDto.getBizUniqueId())).thenReturn(runFlowEntity);
        when(runRuleMapper.selectRunRuleById(runFlowEntity.getRunBizId())).thenReturn(runRuleEntity);
        when(runFlowMapper.updateRunFlow(any(RunFlowEntity.class))).thenReturn(1);
        when(runRuleMapper.updateRunRule(any(RunRuleEntity.class))).thenReturn(1);
        when(runInstanceInfoStateProcessService.processInstanceInfoStateUpdate(anyLong(), anyLong(), anyLong())).thenReturn(true);

        // 执行测试方法
        component.executeMonitorData(monitorFlowDto);

        // 验证调用
        verify(runFlowMapper).selectRunFlowById(monitorFlowDto.getBizUniqueId());
        verify(runRuleMapper).selectRunRuleById(runFlowEntity.getRunBizId());
        verify(runFlowMapper).updateRunFlow(any(RunFlowEntity.class));
        verify(runRuleMapper).updateRunRule(any(RunRuleEntity.class));
        verify(runInstanceInfoStateProcessService).processInstanceInfoStateUpdate(
                runRuleEntity.getEnvcRunInstanceInfoId(), 
                runRuleEntity.getId(), 
                monitorFlowDto.getUpdateOrderTime());
    }

    @Test
    @DisplayName("测试处理流程信息 - 非完成状态")
    void testExecuteMonitorData_FlowInfo_NotCompleted() {
        // 准备测试数据
        monitorFlowDto.setMonitorFowActiveNodeDto(null);
        monitorFlowDto.setFlowStatus(FlowStateEnum.RUNNING.getCode());

        // 设置runFlowEntity的flowid为null或0，模拟需要更新flowid的情况
        runFlowEntity.setFlowid(null);

        // Mock必要的方法调用
        when(runFlowMapper.selectRunFlowById(monitorFlowDto.getBizUniqueId())).thenReturn(runFlowEntity);
        when(runFlowMapper.updateRunFlowOfFirst(any(RunFlowEntity.class))).thenReturn(1);

        // 执行测试方法
        component.executeMonitorData(monitorFlowDto);

        // 验证调用了selectRunFlowById和updateRunFlowOfFirst
        verify(runFlowMapper).selectRunFlowById(monitorFlowDto.getBizUniqueId());
        verify(runFlowMapper).updateRunFlowOfFirst(any(RunFlowEntity.class));

        // 非完成状态不应该调用完成状态相关的数据库操作
        verify(runRuleMapper, never()).selectRunRuleById(anyLong());
        verify(runFlowMapper, never()).updateRunFlow(any(RunFlowEntity.class));
        verify(runRuleMapper, never()).updateRunRule(any(RunRuleEntity.class));
        verify(runInstanceInfoStateProcessService, never()).processInstanceInfoStateUpdate(anyLong(), anyLong(), anyLong());
    }

    @Test
    @DisplayName("测试处理流程信息 - 非完成状态且flowid已存在")
    void testExecuteMonitorData_FlowInfo_NotCompleted_FlowIdExists() {
        // 准备测试数据
        monitorFlowDto.setMonitorFowActiveNodeDto(null);
        monitorFlowDto.setFlowStatus(FlowStateEnum.RUNNING.getCode());

        // 设置runFlowEntity的flowid已存在
        runFlowEntity.setFlowid(123L);

        // Mock必要的方法调用
        when(runFlowMapper.selectRunFlowById(monitorFlowDto.getBizUniqueId())).thenReturn(runFlowEntity);

        // 执行测试方法
        component.executeMonitorData(monitorFlowDto);

        // 验证调用了selectRunFlowById但不调用updateRunFlowOfFirst
        verify(runFlowMapper).selectRunFlowById(monitorFlowDto.getBizUniqueId());
        verify(runFlowMapper, never()).updateRunFlowOfFirst(any(RunFlowEntity.class));

        // 非完成状态不应该调用完成状态相关的数据库操作
        verify(runRuleMapper, never()).selectRunRuleById(anyLong());
        verify(runFlowMapper, never()).updateRunFlow(any(RunFlowEntity.class));
        verify(runRuleMapper, never()).updateRunRule(any(RunRuleEntity.class));
        verify(runInstanceInfoStateProcessService, never()).processInstanceInfoStateUpdate(anyLong(), anyLong(), anyLong());
    }

    @Test
    @DisplayName("测试处理流程信息 - 规则result为-1时设置为1")
    void testExecuteMonitorData_FlowInfo_ResultMinusOne() {
        // 准备测试数据
        monitorFlowDto.setMonitorFowActiveNodeDto(null);
        runRuleEntity.setResult(-1);

        // Mock方法调用
        when(runFlowMapper.selectRunFlowById(monitorFlowDto.getBizUniqueId())).thenReturn(runFlowEntity);
        when(runRuleMapper.selectRunRuleById(runFlowEntity.getRunBizId())).thenReturn(runRuleEntity);
        when(runFlowMapper.updateRunFlow(any(RunFlowEntity.class))).thenReturn(1);
        when(runRuleMapper.updateRunRule(any(RunRuleEntity.class))).thenReturn(1);
        when(runInstanceInfoStateProcessService.processInstanceInfoStateUpdate(anyLong(), anyLong(), anyLong())).thenReturn(true);

        // 执行测试方法
        component.executeMonitorData(monitorFlowDto);

        // 验证调用
        verify(runRuleMapper).updateRunRule(argThat(rule -> rule.getResult() == 1));
    }

    @Test
    @DisplayName("测试处理活动信息 - 文件比对活动")
    void testExecuteMonitorData_ActivityInfo_CompareContent() {
        // 准备测试数据
        monitorFlowDto.setMonitorFowActiveNodeDto(activeNodeDto);
        activeNodeDto.setActDefName(ActivityTypeEnum.COMPARE_CONTENT.getCode());

        List<EngineActOutputBean> actOutputs = new ArrayList<>();
        OutputParseResult parseResult = new OutputParseResult(true, "比对成功");

        // Mock方法调用
        when(runFlowResultEntityMapper.selectRunFlowResultByEnvcRunFlowId(monitorFlowDto.getBizUniqueId())).thenReturn(null);
        when(runFlowMapper.selectRunFlowById(monitorFlowDto.getBizUniqueId())).thenReturn(runFlowEntity);
        when(runRuleMapper.selectRunRuleById(runFlowEntity.getRunBizId())).thenReturn(runRuleEntity);
        when(engineActOutputMapper.queryOutputByReqId(activeNodeDto.getReqId())).thenReturn(actOutputs);
        when(outputParseStrategyFactory.getStrategy(runRuleEntity.getModel(), runRuleEntity.getType())).thenReturn(outputParseStrategy);
        when(outputParseStrategy.parse(actOutputs, activeNodeDto.getActDefName())).thenReturn(parseResult);
        when(runFlowResultEntityMapper.insertRunFlowResult(any(RunFlowResultEntity.class))).thenReturn(1);
        when(runRuleMapper.updateRunRuleOfResult(anyInt(), anyLong())).thenReturn(1);

        // 执行测试方法
        component.executeMonitorData(monitorFlowDto);

        // 验证调用
        verify(runFlowResultEntityMapper).selectRunFlowResultByEnvcRunFlowId(monitorFlowDto.getBizUniqueId());
        verify(runFlowMapper).selectRunFlowById(monitorFlowDto.getBizUniqueId());
        verify(runRuleMapper).selectRunRuleById(runFlowEntity.getRunBizId());
        verify(engineActOutputMapper).queryOutputByReqId(activeNodeDto.getReqId());
        verify(outputParseStrategyFactory).getStrategy(runRuleEntity.getModel(), runRuleEntity.getType());
        verify(outputParseStrategy).parse(actOutputs, activeNodeDto.getActDefName());
        verify(runFlowResultEntityMapper).insertRunFlowResult(any(RunFlowResultEntity.class));
        verify(runRuleMapper).updateRunRuleOfResult(0, runRuleEntity.getId()); // 成功时result为0
    }

    @Test
    @DisplayName("测试处理活动信息 - 文件同步活动")
    void testExecuteMonitorData_ActivityInfo_SyncContent() {
        // 准备测试数据
        monitorFlowDto.setMonitorFowActiveNodeDto(activeNodeDto);
        activeNodeDto.setActDefName(ActivityTypeEnum.SYNC_CONTENT.getCode());

        List<EngineActOutputBean> actOutputs = new ArrayList<>();
        OutputParseResult parseResult = new OutputParseResult(false, "同步失败");

        // Mock方法调用
        when(runFlowResultEntityMapper.selectRunFlowResultByEnvcRunFlowId(monitorFlowDto.getBizUniqueId())).thenReturn(runFlowResultEntity);
        when(runFlowMapper.selectRunFlowById(monitorFlowDto.getBizUniqueId())).thenReturn(runFlowEntity);
        when(runRuleMapper.selectRunRuleById(runFlowEntity.getRunBizId())).thenReturn(runRuleEntity);
        when(engineActOutputMapper.queryOutputByReqId(activeNodeDto.getReqId())).thenReturn(actOutputs);
        when(outputParseStrategyFactory.getStrategy(runRuleEntity.getModel(), runRuleEntity.getType())).thenReturn(outputParseStrategy);
        when(outputParseStrategy.parse(actOutputs, activeNodeDto.getActDefName())).thenReturn(parseResult);
        when(runFlowResultEntityMapper.updateRunFlowResult(any(RunFlowResultEntity.class))).thenReturn(1);
        when(runRuleMapper.updateRunRuleOfResult(anyInt(), anyLong())).thenReturn(1);

        // 执行测试方法
        component.executeMonitorData(monitorFlowDto);

        // 验证调用
        verify(runFlowResultEntityMapper).updateRunFlowResult(any(RunFlowResultEntity.class));
        verify(runRuleMapper).updateRunRuleOfResult(1, runRuleEntity.getId());
    }

    @Test
    @DisplayName("测试处理活动信息 - Shell命令活动")
    void testExecuteMonitorData_ActivityInfo_ShellCmd() {
        // 准备测试数据
        monitorFlowDto.setMonitorFowActiveNodeDto(activeNodeDto);
        activeNodeDto.setActDefName(ActivityTypeEnum.SHELL_CMD.getCode());
        activeNodeDto.setActName(ScriptActivityNameEnum.COMPARE_FILE_SSH.getCode());

        List<EngineActOutputBean> actOutputs = new ArrayList<>();
        OutputParseResult parseResult = new OutputParseResult(true, "脚本执行成功");

        // Mock方法调用
        when(runFlowResultEntityMapper.selectRunFlowResultByEnvcRunFlowId(monitorFlowDto.getBizUniqueId())).thenReturn(null);
        when(runFlowMapper.selectRunFlowById(monitorFlowDto.getBizUniqueId())).thenReturn(runFlowEntity);
        when(runRuleMapper.selectRunRuleById(runFlowEntity.getRunBizId())).thenReturn(runRuleEntity);
        when(engineActOutputMapper.queryOutputByReqId(activeNodeDto.getReqId())).thenReturn(actOutputs);
        when(outputParseStrategyFactory.getStrategy(runRuleEntity.getModel(), runRuleEntity.getType())).thenReturn(outputParseStrategy);
        when(outputParseStrategy.parse(actOutputs, activeNodeDto.getActDefName())).thenReturn(parseResult);
        when(runFlowResultEntityMapper.insertRunFlowResult(any(RunFlowResultEntity.class))).thenReturn(1);
        when(runRuleMapper.updateRunRuleOfResult(anyInt(), anyLong())).thenReturn(1);

        // 执行测试方法
        component.executeMonitorData(monitorFlowDto);

        // 验证调用
        verify(runFlowResultEntityMapper).insertRunFlowResult(any(RunFlowResultEntity.class));
        verify(runRuleMapper).updateRunRuleOfResult(0, runRuleEntity.getId());
    }

    @Test
    @DisplayName("测试处理活动信息 - 策略为空")
    void testExecuteMonitorData_ActivityInfo_StrategyNull() {
        // 准备测试数据
        monitorFlowDto.setMonitorFowActiveNodeDto(activeNodeDto);
        activeNodeDto.setActDefName(ActivityTypeEnum.COMPARE_CONTENT.getCode());

        List<EngineActOutputBean> actOutputs = new ArrayList<>();

        // Mock方法调用
        when(runFlowResultEntityMapper.selectRunFlowResultByEnvcRunFlowId(monitorFlowDto.getBizUniqueId())).thenReturn(null);
        when(runFlowMapper.selectRunFlowById(monitorFlowDto.getBizUniqueId())).thenReturn(runFlowEntity);
        when(runRuleMapper.selectRunRuleById(runFlowEntity.getRunBizId())).thenReturn(runRuleEntity);
        when(engineActOutputMapper.queryOutputByReqId(activeNodeDto.getReqId())).thenReturn(actOutputs);
        when(outputParseStrategyFactory.getStrategy(runRuleEntity.getModel(), runRuleEntity.getType())).thenReturn(null);
        when(runFlowResultEntityMapper.insertRunFlowResult(any(RunFlowResultEntity.class))).thenReturn(1);
        when(runRuleMapper.updateRunRuleOfResult(anyInt(), anyLong())).thenReturn(1);

        // 执行测试方法
        component.executeMonitorData(monitorFlowDto);

        // 验证调用
        verify(runFlowResultEntityMapper).insertRunFlowResult(argThat(entity -> "".equals(entity.getContent())));
        verify(runRuleMapper).updateRunRuleOfResult(1, runRuleEntity.getId()); // 失败时result为1
    }

    @Test
    @DisplayName("测试处理活动信息 - 活动状态不满足条件")
    void testExecuteMonitorData_ActivityInfo_InvalidActivityState() {
        // 准备测试数据
        monitorFlowDto.setMonitorFowActiveNodeDto(activeNodeDto);
        activeNodeDto.setActDefName(ActivityTypeEnum.COMPARE_CONTENT.getCode());
        activeNodeDto.setActStatus(ActivityStateEnum.ACT_STATE_RUNNING.getCode());

        // 执行测试方法
        component.executeMonitorData(monitorFlowDto);

        // 不满足条件时不应该有数据库操作
        verify(runFlowResultEntityMapper, never()).selectRunFlowResultByEnvcRunFlowId(anyLong());
        verify(runFlowMapper, never()).selectRunFlowById(anyLong());
        verify(runRuleMapper, never()).selectRunRuleById(anyLong());
    }

    @Test
    @DisplayName("测试处理活动信息 - 活动类型不满足条件")
    void testExecuteMonitorData_ActivityInfo_InvalidActivityType() {
        // 准备测试数据
        monitorFlowDto.setMonitorFowActiveNodeDto(activeNodeDto);
        activeNodeDto.setActDefName("INVALID_ACTIVITY_TYPE");

        // 执行测试方法
        component.executeMonitorData(monitorFlowDto);

        // 验证没有调用相关方法
        verify(runFlowResultEntityMapper, never()).selectRunFlowResultByEnvcRunFlowId(anyLong());
        verify(engineActOutputMapper, never()).queryOutputByReqId(anyString());
    }

    @Test
    @DisplayName("测试处理活动信息 - Shell命令活动名称无效")
    void testExecuteMonitorData_ActivityInfo_InvalidShellActivityName() {
        // 准备测试数据
        monitorFlowDto.setMonitorFowActiveNodeDto(activeNodeDto);
        activeNodeDto.setActDefName(ActivityTypeEnum.SHELL_CMD.getCode());
        activeNodeDto.setActName("INVALID_SHELL_ACTIVITY");

        // 执行测试方法
        component.executeMonitorData(monitorFlowDto);

        // 验证没有调用相关方法
        verify(runFlowResultEntityMapper, never()).selectRunFlowResultByEnvcRunFlowId(anyLong());
        verify(engineActOutputMapper, never()).queryOutputByReqId(anyString());
    }

    @Test
    @DisplayName("测试处理活动信息 - 活动节点为空")
    void testExecuteMonitorData_ActivityInfo_NullActiveNode() {
        // 准备测试数据
        monitorFlowDto.setMonitorFowActiveNodeDto(null);

        // Mock方法调用
        when(runFlowMapper.selectRunFlowById(monitorFlowDto.getBizUniqueId())).thenReturn(runFlowEntity);
        when(runRuleMapper.selectRunRuleById(runFlowEntity.getRunBizId())).thenReturn(runRuleEntity);
        when(runFlowMapper.updateRunFlow(any(RunFlowEntity.class))).thenReturn(1);
        when(runRuleMapper.updateRunRule(any(RunRuleEntity.class))).thenReturn(1);
        when(runInstanceInfoStateProcessService.processInstanceInfoStateUpdate(anyLong(), anyLong(), anyLong())).thenReturn(true);

        // 执行测试方法
        component.executeMonitorData(monitorFlowDto);

        // 验证调用流程信息处理方法
        verify(runFlowMapper).selectRunFlowById(monitorFlowDto.getBizUniqueId());
    }

    @Test
    @DisplayName("测试处理活动信息 - 活动节点不为空但canFlowContentModify返回false")
    void testExecuteMonitorData_ActivityInfo_CanFlowContentModifyFalse() {
        // 准备测试数据
        monitorFlowDto.setMonitorFowActiveNodeDto(activeNodeDto);
        activeNodeDto.setActStatus(ActivityStateEnum.ACT_STATE_RUNNING.getCode());

        // 执行测试方法
        component.executeMonitorData(monitorFlowDto);

        // 验证没有调用相关方法
        verify(runFlowResultEntityMapper, never()).selectRunFlowResultByEnvcRunFlowId(anyLong());
    }

    @Test
    @DisplayName("测试消息验证 - 有效消息")
    void testCheckMonitorMessage_ValidMessage() {
        // Mock方法调用
        when(runFlowMapper.selectRunFlowById(monitorFlowDto.getBizUniqueId())).thenReturn(runFlowEntity);
        when(runRuleMapper.selectRunRuleById(runFlowEntity.getRunBizId())).thenReturn(runRuleEntity);
        when(runFlowMapper.updateRunFlow(any(RunFlowEntity.class))).thenReturn(1);
        when(runRuleMapper.updateRunRule(any(RunRuleEntity.class))).thenReturn(1);
        when(runInstanceInfoStateProcessService.processInstanceInfoStateUpdate(anyLong(), anyLong(), anyLong())).thenReturn(true);

        // 执行测试方法
        component.executeMonitorData(monitorFlowDto);

        // 验证正常处理
        verify(runFlowMapper).selectRunFlowById(monitorFlowDto.getBizUniqueId());
    }

    @Test
    @DisplayName("测试消息验证 - null消息")
    void testCheckMonitorMessage_NullMessage() {
        // 执行测试方法
        component.executeMonitorData(null);

        // 验证没有调用任何方法
        verify(runFlowMapper, never()).selectRunFlowById(anyLong());
    }

    @Test
    @DisplayName("测试消息验证 - flowId为null")
    void testCheckMonitorMessage_NullFlowId() {
        // 准备测试数据
        monitorFlowDto.setFlowId(null);

        // 执行测试方法
        component.executeMonitorData(monitorFlowDto);

        // 验证没有调用任何方法
        verify(runFlowMapper, never()).selectRunFlowById(anyLong());
    }

    @Test
    @DisplayName("测试消息验证 - flowId小于等于0")
    void testCheckMonitorMessage_InvalidFlowId() {
        // 准备测试数据
        monitorFlowDto.setFlowId(0L);

        // 执行测试方法
        component.executeMonitorData(monitorFlowDto);

        // 验证没有调用任何方法
        verify(runFlowMapper, never()).selectRunFlowById(anyLong());
    }

    @Test
    @DisplayName("测试消息验证 - flowStatus为null")
    void testCheckMonitorMessage_NullFlowStatus() {
        // 准备测试数据
        monitorFlowDto.setFlowStatus(null);

        // 执行测试方法
        component.executeMonitorData(monitorFlowDto);

        // 验证没有调用任何方法
        verify(runFlowMapper, never()).selectRunFlowById(anyLong());
    }

    @Test
    @DisplayName("测试消息验证 - bizUniqueId为null")
    void testCheckMonitorMessage_NullBizUniqueId() {
        // 准备测试数据
        monitorFlowDto.setBizUniqueId(null);

        // 执行测试方法
        component.executeMonitorData(monitorFlowDto);

        // 验证没有调用任何方法
        verify(runFlowMapper, never()).selectRunFlowById(anyLong());
    }

    @Test
    @DisplayName("测试消息验证 - bizUniqueId小于等于0")
    void testCheckMonitorMessage_InvalidBizUniqueId() {
        // 准备测试数据
        monitorFlowDto.setBizUniqueId(0L);

        // 执行测试方法
        component.executeMonitorData(monitorFlowDto);

        // 验证没有调用任何方法
        verify(runFlowMapper, never()).selectRunFlowById(anyLong());
    }

    @Test
    @DisplayName("测试消息验证 - dateTime为null")
    void testCheckMonitorMessage_NullDateTime() {
        // 准备测试数据
        monitorFlowDto.setDateTime(null);

        // 执行测试方法
        component.executeMonitorData(monitorFlowDto);

        // 验证没有调用任何方法
        verify(runFlowMapper, never()).selectRunFlowById(anyLong());
    }

    @Test
    @DisplayName("测试处理活动信息 - 各种活动状态")
    void testExecuteMonitorData_ActivityInfo_VariousActivityStates() {
        // 测试各种活动状态
        String[] validStates = {
                ActivityStateEnum.ACT_STATE_FINISH.getCode(),
                ActivityStateEnum.ACT_STATE_FAIL.getCode(),
                ActivityStateEnum.ACT_STATE_FAIL_BUSINESS.getCode(),
                ActivityStateEnum.ACT_STATE_FAIL_SKIPPED.getCode(),
                ActivityStateEnum.ACT_STATE_SKIPPED.getCode()
        };

        for (String state : validStates) {
            // 准备测试数据
            monitorFlowDto.setMonitorFowActiveNodeDto(activeNodeDto);
            activeNodeDto.setActStatus(state);
            activeNodeDto.setActDefName(ActivityTypeEnum.COMPARE_CONTENT.getCode());

            List<EngineActOutputBean> actOutputs = new ArrayList<>();
            OutputParseResult parseResult = new OutputParseResult(true, "测试内容");

            // Mock方法调用
            when(runFlowResultEntityMapper.selectRunFlowResultByEnvcRunFlowId(monitorFlowDto.getBizUniqueId())).thenReturn(null);
            when(runFlowMapper.selectRunFlowById(monitorFlowDto.getBizUniqueId())).thenReturn(runFlowEntity);
            when(runRuleMapper.selectRunRuleById(runFlowEntity.getRunBizId())).thenReturn(runRuleEntity);
            when(engineActOutputMapper.queryOutputByReqId(activeNodeDto.getReqId())).thenReturn(actOutputs);
            when(outputParseStrategyFactory.getStrategy(runRuleEntity.getModel(), runRuleEntity.getType())).thenReturn(outputParseStrategy);
            when(outputParseStrategy.parse(actOutputs, activeNodeDto.getActDefName())).thenReturn(parseResult);
            when(runFlowResultEntityMapper.insertRunFlowResult(any(RunFlowResultEntity.class))).thenReturn(1);
            when(runRuleMapper.updateRunRuleOfResult(anyInt(), anyLong())).thenReturn(1);

            // 执行测试方法
            component.executeMonitorData(monitorFlowDto);

            // 验证调用
            verify(runFlowResultEntityMapper).insertRunFlowResult(any(RunFlowResultEntity.class));
            verify(runRuleMapper).updateRunRuleOfResult(0, runRuleEntity.getId());

            // 重置Mock
            reset(runFlowResultEntityMapper, runRuleMapper, runFlowMapper, runInstanceInfoStateProcessService, engineActOutputMapper, outputParseStrategyFactory, outputParseStrategy);
        }
    }

    @Test
    @DisplayName("测试处理活动信息 - 文件存储启用")
    void testExecuteMonitorData_ActivityInfo_FileStorageEnabled() {
        // 准备测试数据
        monitorFlowDto.setMonitorFowActiveNodeDto(activeNodeDto);
        activeNodeDto.setActDefName(ActivityTypeEnum.COMPARE_CONTENT.getCode());
        activeNodeDto.setActStatus(ActivityStateEnum.ACT_STATE_FINISH.getCode());

        // 确保runRuleEntity有正确的值
        runRuleEntity.setModel(1);
        runRuleEntity.setType(1L);

        List<EngineActOutputBean> actOutputs = new ArrayList<>();
        OutputParseResult parseResult = new OutputParseResult(true, "比对结果内容");

        when(runFlowResultEntityMapper.selectRunFlowResultByEnvcRunFlowId(anyLong())).thenReturn(runFlowResultEntity);
        when(runRuleMapper.selectRunRuleById(anyLong())).thenReturn(runRuleEntity);
        when(engineActOutputMapper.queryOutputByReqId(anyString())).thenReturn(actOutputs);
        when(outputParseStrategyFactory.getStrategy(anyInt(), anyLong())).thenReturn(outputParseStrategy);
        when(outputParseStrategy.parse(any(), anyString())).thenReturn(parseResult);

        // Mock文件存储配置
        when(contrastResultConfig.isFileStorageEnabled()).thenReturn(true);
        when(fileOperationService.generateResultFilePath(anyString())).thenReturn("/test/path/result.json");
        when(fileOperationService.saveResultFileOfPath(anyString(), anyString())).thenReturn(true);

        // 执行测试方法
        component.executeMonitorData(monitorFlowDto);

        // 验证文件操作被调用
        verify(fileOperationService).generateResultFilePath(monitorFlowDto.getFlowId().toString());
        verify(fileOperationService).saveResultFileOfPath(eq("比对结果内容"), eq("/test/path/result.json"));
        verify(runFlowResultEntityMapper).updateRunFlowResult(any(RunFlowResultEntity.class));
    }

    @Test
    @DisplayName("测试处理活动信息 - 文件存储未启用")
    void testExecuteMonitorData_ActivityInfo_FileStorageDisabled() {
        // 准备测试数据
        monitorFlowDto.setMonitorFowActiveNodeDto(activeNodeDto);
        activeNodeDto.setActDefName(ActivityTypeEnum.COMPARE_CONTENT.getCode());
        activeNodeDto.setActStatus(ActivityStateEnum.ACT_STATE_FINISH.getCode());

        // 确保runRuleEntity有正确的值
        runRuleEntity.setModel(1);
        runRuleEntity.setType(1L);

        List<EngineActOutputBean> actOutputs = new ArrayList<>();
        OutputParseResult parseResult = new OutputParseResult(true, "比对结果内容");

        when(runFlowResultEntityMapper.selectRunFlowResultByEnvcRunFlowId(anyLong())).thenReturn(runFlowResultEntity);
        when(runRuleMapper.selectRunRuleById(anyLong())).thenReturn(runRuleEntity);
        when(engineActOutputMapper.queryOutputByReqId(anyString())).thenReturn(actOutputs);
        when(outputParseStrategyFactory.getStrategy(anyInt(), anyLong())).thenReturn(outputParseStrategy);
        when(outputParseStrategy.parse(any(), anyString())).thenReturn(parseResult);

        // Mock文件存储配置为未启用
        when(contrastResultConfig.isFileStorageEnabled()).thenReturn(false);

        // 执行测试方法
        component.executeMonitorData(monitorFlowDto);

        // 验证文件操作没有被调用
        verify(fileOperationService, never()).generateResultFilePath(anyString());
        verify(fileOperationService, never()).saveResultFileOfPath(anyString(), anyString());
        verify(runFlowResultEntityMapper).updateRunFlowResult(any(RunFlowResultEntity.class));
    }
}